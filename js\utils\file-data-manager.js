/**
 * File Data Manager
 * Centralized management of file data loading, processing, storage, and cleanup
 */

import IndexedDBStorage from './indexeddb-storage.js';

class FileDataManager {
    constructor(options = {}) {
        this.options = {
            maxFileSize: options.maxFileSize || 10 * 1024 * 1024, // 10MB
            maxTotalSize: options.maxTotalSize || 100 * 1024 * 1024, // 100MB
            enableStorage: options.enableStorage !== false,
            storageOptions: options.storageOptions || {},
            ...options
        };

        this.storage = null;
        this.currentData = null;
        this.loadingState = {
            isLoading: false,
            progress: 0,
            phase: 'idle',
            filesProcessed: 0,
            totalFiles: 0
        };

        // Initialize storage if enabled
        if (this.options.enableStorage) {
            this.initializeStorage();
        }
    }

    /**
     * Initialize IndexedDB storage
     */
    async initializeStorage() {
        try {
            this.storage = new IndexedDBStorage({
                dbName: 'DependencyVisualizerDB',
                maxStorageSize: 50 * 1024 * 1024, // 50MB
                maxAge: 3 * 24 * 60 * 60 * 1000, // 3 days
                ...this.options.storageOptions
            });
            await this.storage.initialize();
            console.log('File Data Manager: Storage initialized');
        } catch (error) {
            console.warn('File Data Manager: Storage initialization failed:', error);
            this.storage = null;
        }
    }

    /**
     * Load and process files with comprehensive error handling
     */
    async loadFiles(files, options = {}) {
        if (this.loadingState.isLoading) {
            throw new Error('Another file loading operation is already in progress');
        }

        this.loadingState = {
            isLoading: true,
            progress: 0,
            phase: 'validation',
            filesProcessed: 0,
            totalFiles: files.length
        };

        try {
            // Phase 1: Validate and filter files
            this.updateProgress('validation', 0);
            const validFiles = await this.validateAndFilterFiles(files);
            
            // Phase 2: Load file contents
            this.updateProgress('loading', 10);
            const fileContents = await this.loadFileContents(validFiles);
            
            // Phase 3: Process files
            this.updateProgress('processing', 50);
            const processedData = await this.processFiles(fileContents, options);
            
            // Phase 4: Store results
            this.updateProgress('storing', 90);
            await this.storeResults(processedData);
            
            // Phase 5: Cleanup
            this.updateProgress('cleanup', 95);
            await this.cleanupTemporaryData(fileContents);
            
            this.updateProgress('complete', 100);
            this.currentData = processedData;
            
            return {
                success: true,
                data: processedData,
                stats: this.getLoadingStats()
            };

        } catch (error) {
            console.error('File Data Manager: Loading failed:', error);
            return {
                success: false,
                error: {
                    message: error.message,
                    type: error.name || 'LoadingError',
                    phase: this.loadingState.phase
                },
                stats: this.getLoadingStats()
            };
        } finally {
            this.loadingState.isLoading = false;
        }
    }

    /**
     * Validate and filter files before processing
     */
    async validateAndFilterFiles(files) {
        const validFiles = [];
        let totalSize = 0;

        for (const file of files) {
            // Check file size
            if (file.size > this.options.maxFileSize) {
                console.warn(`File too large: ${file.name} (${file.size} bytes)`);
                continue;
            }

            // Check total size limit
            if (totalSize + file.size > this.options.maxTotalSize) {
                console.warn(`Total size limit exceeded, skipping remaining files`);
                break;
            }

            // Check file type
            if (this.isValidFileType(file)) {
                validFiles.push(file);
                totalSize += file.size;
            }
        }

        console.log(`File Data Manager: Validated ${validFiles.length} of ${files.length} files`);
        return validFiles;
    }

    /**
     * Check if file type is supported
     */
    isValidFileType(file) {
        const supportedExtensions = [
            'js', 'mjs', 'jsx', 'ts', 'tsx',
            'css', 'scss', 'sass', 'less',
            'html', 'htm', 'json'
        ];

        const extension = file.name.split('.').pop()?.toLowerCase();
        return supportedExtensions.includes(extension);
    }

    /**
     * Load file contents with memory management
     */
    async loadFileContents(files) {
        const fileContents = [];
        const batchSize = 10; // Process files in batches

        for (let i = 0; i < files.length; i += batchSize) {
            const batch = files.slice(i, i + batchSize);
            const batchPromises = batch.map(file => this.readSingleFile(file));
            
            try {
                const batchResults = await Promise.all(batchPromises);
                fileContents.push(...batchResults.filter(result => result !== null));
                
                // Update progress
                const progress = 10 + Math.round((i / files.length) * 40);
                this.updateProgress('loading', progress);
                
                // Yield control to prevent blocking
                await this.yieldControl();
                
            } catch (error) {
                console.warn(`File Data Manager: Batch loading error:`, error);
            }
        }

        return fileContents;
    }

    /**
     * Read a single file with error handling
     */
    async readSingleFile(file) {
        return new Promise((resolve) => {
            const reader = new FileReader();
            
            reader.onload = () => {
                resolve({
                    path: file.webkitRelativePath || file.name,
                    content: reader.result,
                    size: file.size,
                    lastModified: file.lastModified
                });
            };
            
            reader.onerror = () => {
                console.warn(`Failed to read file: ${file.name}`);
                resolve(null);
            };
            
            // Set timeout for file reading
            setTimeout(() => {
                if (reader.readyState === FileReader.LOADING) {
                    reader.abort();
                    console.warn(`File reading timeout: ${file.name}`);
                    resolve(null);
                }
            }, 30000); // 30 second timeout
            
            reader.readAsText(file);
        });
    }

    /**
     * Process files using the enhanced file processor
     */
    async processFiles(fileContents, options) {
        // This will be called by the enhanced file processor
        // We're just managing the data flow here
        return fileContents;
    }

    /**
     * Store results to IndexedDB
     */
    async storeResults(data) {
        if (!this.storage || !data) return;

        try {
            const projectPath = this.generateProjectPath(data);
            await this.storage.storeScanResults(data, projectPath);
            
            const stats = await this.storage.getStats();
            console.log('File Data Manager: Storage stats:', stats);
            
        } catch (error) {
            console.warn('File Data Manager: Storage failed:', error);
        }
    }

    /**
     * Generate a project path identifier
     */
    generateProjectPath(data) {
        if (data.metadata?.projectPath) {
            return data.metadata.projectPath;
        }
        
        // Generate based on file paths
        const paths = Object.keys(data.files || {});
        if (paths.length > 0) {
            const commonPath = this.findCommonPath(paths);
            return commonPath || 'unknown-project';
        }
        
        return `project-${Date.now()}`;
    }

    /**
     * Find common path among file paths
     */
    findCommonPath(paths) {
        if (paths.length === 0) return '';
        if (paths.length === 1) return paths[0].split('/').slice(0, -1).join('/');
        
        const segments = paths[0].split('/');
        let commonLength = 0;
        
        for (let i = 0; i < segments.length - 1; i++) {
            if (paths.every(path => path.split('/')[i] === segments[i])) {
                commonLength = i + 1;
            } else {
                break;
            }
        }
        
        return segments.slice(0, commonLength).join('/');
    }

    /**
     * Clean up temporary data and references
     */
    async cleanupTemporaryData(fileContents) {
        if (!fileContents) return;

        // Clear file content references
        fileContents.forEach(file => {
            if (file) {
                file.content = null;
            }
        });

        // Clear the array
        fileContents.length = 0;

        // Force garbage collection if available
        if (window.gc) {
            setTimeout(() => window.gc(), 100);
        }

        console.log('File Data Manager: Temporary data cleaned up');
    }

    /**
     * Update loading progress
     */
    updateProgress(phase, progress) {
        this.loadingState.phase = phase;
        this.loadingState.progress = progress;
        
        // Dispatch progress event
        if (typeof window !== 'undefined') {
            window.dispatchEvent(new CustomEvent('fileDataManager:progress', {
                detail: { ...this.loadingState }
            }));
        }
    }

    /**
     * Get loading statistics
     */
    getLoadingStats() {
        return {
            ...this.loadingState,
            hasStorage: !!this.storage,
            currentDataSize: this.currentData ? this.estimateSize(this.currentData) : 0
        };
    }

    /**
     * Estimate object size
     */
    estimateSize(obj) {
        try {
            return new Blob([JSON.stringify(obj)]).size;
        } catch (error) {
            return 0;
        }
    }

    /**
     * Yield control to prevent blocking
     */
    async yieldControl() {
        return new Promise(resolve => setTimeout(resolve, 0));
    }

    /**
     * Get current data
     */
    getCurrentData() {
        return this.currentData;
    }

    /**
     * Clear current data
     */
    clearCurrentData() {
        this.currentData = null;
    }

    /**
     * Get storage statistics
     */
    async getStorageStats() {
        if (!this.storage) return null;
        return await this.storage.getStats();
    }

    /**
     * Clean up all resources
     */
    async cleanup() {
        this.clearCurrentData();
        
        if (this.storage) {
            await this.storage.cleanup();
            this.storage.close();
            this.storage = null;
        }
        
        console.log('File Data Manager: Cleanup completed');
    }
}

export default FileDataManager;
